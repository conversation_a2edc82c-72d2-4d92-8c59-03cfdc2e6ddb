# /etc/nginx/nginx.conf

user nginx;
worker_processes auto;

events {
    worker_connections 1024;  # 最大连接数
}
http {
    types {
        text/html                             html htm shtml;
        text/css                              css;
        # text/xml                              xml;
        image/gif                             gif;
        image/jpeg                            jpeg jpg;
        application/javascript                js;
        application/font-woff                 woff;
        application/font-ttf                  ttf;
        application/json                      json;
        application/xml                       xml;
        application/vnd.ms-fontobject         eot;
        image/svg+xml                         svg;
        video/mp4                             mp4;
        video/x-msvideo                       avi;
        video/quicktime                       mov;
        video/mpeg                            mpeg mpg;
        video/webm                            webm;
    }

    map $http_origin $cors_origin {
        ~^https?://[a-zA-Z0-9\-]+\.mayohr\.com(:[0-9]+)?$ $http_origin;
        ~^http://localhost:3000$ $http_origin;
        ~^http://localhost:5173$ $http_origin;
        default "";
    }

    server {
            listen       8080;
            server_name  localhost;

            gzip  on;
            gzip_min_length 1k;
            gzip_comp_level 5;
            gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
            gzip_disable "MSIE [1-6]\.";
            gzip_vary on;

            location /proxy/ {
                add_header Access-Control-Allow-Origin $cors_origin;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                add_header Access-Control-Allow-Credentials true;
                
                if ($request_method = 'OPTIONS') {
                    return 204;
                }
                
                proxy_pass https://apolloxe.mayohr.com/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }

            location /uat/proxy/ {
                add_header Access-Control-Allow-Origin $cors_origin;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                add_header Access-Control-Allow-Credentials true;
                
                if ($request_method = 'OPTIONS') {
                    return 204;
                }
                
                proxy_pass https://uat-apolloxe.mayohr.com/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }

           location /tst/proxy/ {
                # 使用動態 CORS 設定
                add_header Access-Control-Allow-Origin $cors_origin;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                add_header Access-Control-Allow-Credentials true;
                
                if ($request_method = 'OPTIONS') {
                    add_header Access-Control-Allow-Origin $cors_origin;
                    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                    add_header Access-Control-Allow-Credentials true;
                    add_header Access-Control-Max-Age 86400;
                    return 204;
                }
                
                proxy_pass https://tst-apolloxe.mayohr.com/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            } 

            location /langgraph/shift-scheduling-agent/ {
                proxy_pass https://shift-scheduling-agent-71024d21f4125f6a8293ad5f8025a863.us.langgraph.app/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                # proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header x-api-key "***************************************************";
                # proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /uat/langgraph/shift-scheduling-agent/ {
                proxy_pass https://uat-shift-scheduling-agent-1d5a04ba60735976871189e474344d60.us.langgraph.app/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                # proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header x-api-key "***************************************************";
                # proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /tst/langgraph/shift-scheduling-agent/ {
                proxy_pass https://uat-shift-scheduling-agent-1d5a04ba60735976871189e474344d60.us.langgraph.app/;
                proxy_ssl_server_name on;
                proxy_set_header PXHost  $host;
                # proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header x-api-key "***************************************************";
                # proxy_set_header X-Forwarded-Proto $scheme;
            }

            # 健康檢查端點
            location /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
            }

            location / {
                    root /usr/share/nginx/html;
                    index index.php index.html index.htm;
                    # add_header Cache-Control;
                    add_header Access-Control-Allow-Origin *;
                    if ( $request_uri ~* ^.+.(js|css|jpg|png|gif|tif|dpg|jpeg|eot|svg|ttf|woff|json|mp4|rmvb|rm|wmv|avi|3gp)$ ){
                            add_header Cache-Control max-age=7776000;
                            add_header Access-Control-Allow-Origin *;
                    }
            }
    }
}

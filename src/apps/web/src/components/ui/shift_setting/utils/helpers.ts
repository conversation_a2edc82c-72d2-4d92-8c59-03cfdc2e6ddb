import { START_OF_DAY } from '../constants';

// 時間相關工具函數
export const getTimeValue = (time: string): number => {
  if (!time) return 0;
  const timeMoment = parseInt(time.replace(':', ''), 10);
  const startMoment = parseInt(START_OF_DAY.replace(':', ''), 10);
  return timeMoment < startMoment ? timeMoment + 2400 : timeMoment;
};

// 時間格式化工具函數
export const formatTime = (hour: number, minute: number = 0): string => {
  return `${String(hour % 24).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;
};

// 時間排序工具函數
export const sortTimeslots = (timeslots: string[]): string[] => {
  return timeslots
    .slice()
    .sort((a, b) => getTimeValue(a.split('-')[0]) - getTimeValue(b.split('-')[0]));
};

// 時間區間重疊檢查
export const isTimeOverlapping = (
  existingSlots: string[],
  newStart: string,
  newEnd: string
): boolean => {
  return existingSlots.some((slot) => {
    const [existingStart, existingEnd] = slot.split('-');
    return (
      getTimeValue(newStart) < getTimeValue(existingEnd) &&
      getTimeValue(existingStart) < getTimeValue(newEnd)
    );
  });
};

// 時間驗證工具函數
export const validateTimeRange = (start: string, end: string): boolean => {
  return !!(start && end && getTimeValue(start) < getTimeValue(end));
};

// 產生下一個時間段建議
export const generateNextTimeslot = (timeslots: string[]): { start: string; end: string } => {
  const sorted = sortTimeslots(timeslots);
  const lastSlot = sorted[sorted.length - 1] || '08:00-09:00';
  const lastEndTime = lastSlot.split('-')[1];
  const nextStartHour = parseInt(lastEndTime.split(':')[0], 10);

  return {
    start: lastEndTime,
    end: formatTime(nextStartHour + 1),
  };
};

// 字串排序工具函數（支援中文）
export const sortByName = (a: { name: string }, b: { name: string }): number => {
  if (a.name && b.name) {
    return a.name.localeCompare(b.name, 'zh-Hant');
  }
  return 0;
};

// 深拷貝工具函數
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// 生成唯一 ID
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 防抖函數
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 搜尋過濾工具函數
export const filterBySearch = <T>(
  items: T[],
  searchTerm: string,
  searchFields: (keyof T)[]
): T[] => {
  if (!searchTerm) return items;

  const term = searchTerm.toLowerCase();
  return items.filter((item) =>
    searchFields.some((field) => {
      const value = item[field];
      return typeof value === 'string' && value.toLowerCase().includes(term);
    })
  );
};

// 陣列工具函數
export const toggleArrayItem = <T>(array: T[], item: T): T[] => {
  const index = array.indexOf(item);
  if (index > -1) {
    return array.filter((_, i) => i !== index);
  } else {
    return [...array, item];
  }
};

// 物件工具函數
export const isEmptyObject = (obj: any): boolean => {
  return Object.keys(obj).length === 0;
};

// 錯誤處理工具函數
export const getErrorMessage = (error: any): string => {
  console.log('API 錯誤:', error);
  if (typeof error === 'string') return error;

  const message = error?.message || '';
  const data = error?.data || '';

  // 如果兩者都有，用冒號分隔
  if (message && data) {
    return `${message}: ${data}`;
  }

  // 如果只有其中一個，直接返回
  if (message) return message;
  if (data) return data;

  // 都沒有則返回預設錯誤
  return '未知錯誤';
};

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  Schedule,
  StaffData,
  ActionModalConfig,
  Role,
  SaveStatus,
  ActionType,
  Employee,
} from '../types';
import { shiftApi, employeeApi } from '../services/api';
import {
  transformJsonToState,
  transformStateToJson,
  enhanceScheduleWithDisplayText,
  generateStaffAssignments,
  createDefaultRole,
} from '../utils/data-transform';
import {
  deepClone,
  getErrorMessage,
  validateTimeRange,
  isTimeOverlapping,
  generateNextTimeslot,
} from '../utils/helpers';
import { useParentData } from '@/providers/ParentData';

interface UseShiftProps {
  initialDepartmentId?: string;
}

interface UseShiftReturn {
  // 狀態
  currentDepartmentId: string;
  currentSchedule: Schedule | null;
  staffData: StaffData;
  currentEmployees: Employee[];
  isLoading: boolean;
  loadingText: string;
  saveStatus: SaveStatus;
  isRosterModalOpen: boolean;
  editingRole: Role | null;
  actionModalConfig: ActionModalConfig | null;

  // 計算屬性
  scheduleWithRosterText: Schedule | null;
  staffAssignments: { [staffId: string]: string[] };

  // 動作
  setCurrentDepartmentId: (departmentId: string) => void;
  loadData: () => Promise<void>;
  handleSaveSettings: () => Promise<void>;
  handleScheduleUpdate: (
    roleId: string,
    field: 'roleName' | 'remarks' | 'headcount' | 'roster',
    value: string | number | string[],
    timeslot?: string | null
  ) => void;
  handleAddRole: () => void;
  showSaveStatus: (msg: string, isError?: boolean) => void;

  // Modal 相關
  openRosterModal: (role: Role) => void;
  closeRosterModal: () => void;
  showActionModal: (
    config: Omit<ActionModalConfig, 'onConfirm' | 'onClose' | 'title' | 'message' | 'mode'> & {
      type: ActionType;
      data?: any;
    }
  ) => void;
  closeActionModal: () => void;
  handleActionConfirm: (type: ActionType, data: any) => void;
}

export const useShift = ({ initialDepartmentId = '' }: UseShiftProps = {}): UseShiftReturn => {
  // 父層應用資料
  const { processedData } = useParentData();
  // 基礎狀態
  const [currentDepartmentId, setCurrentDepartmentId] = useState<string>(initialDepartmentId);
  const [currentSchedule, setCurrentSchedule] = useState<Schedule | null>(null);
  const [staffData, setStaffData] = useState<StaffData>({});
  const [currentEmployees, setCurrentEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingText, setLoadingText] = useState<string>('讀取設定...');
  const [saveStatus, setSaveStatus] = useState<SaveStatus>({
    msg: '',
    visible: false,
    isError: false,
  });

  // Modal 狀態
  const [isRosterModalOpen, setIsRosterModalOpen] = useState<boolean>(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [actionModalConfig, setActionModalConfig] = useState<ActionModalConfig | null>(null);

  // Refs
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // 狀態管理函數
  const showSaveStatus = useCallback((msg: string, isError: boolean = false) => {
    setSaveStatus({ msg, visible: true, isError });
    if (debounceTimer.current) clearTimeout(debounceTimer.current);
    if (!isError) {
      debounceTimer.current = setTimeout(() => {
        setSaveStatus((prev) => ({ ...prev, visible: false }));
      }, 3000);
    }
  }, []);

  // 資料載入
  const loadData = useCallback(async () => {
    setIsLoading(true);
    setLoadingText('讀取設定...');

    try {
      // 同時載入排班設定和員工資料
      const [requirementsResult, employeesResult] = await Promise.all([
        shiftApi.getRequirements(currentDepartmentId),
        employeeApi.getEmployeesByDepartmentIds([currentDepartmentId]),
      ]);

      // 處理排班設定
      if (requirementsResult?.error) {
        throw new Error(requirementsResult.error.message);
      }

      if (requirementsResult.data) {
        const { schedule, staff } = transformJsonToState(
          requirementsResult.data,
          currentDepartmentId
        );
        setCurrentSchedule(schedule);
        setStaffData(staff);
      } else {
        throw new Error('從 API 獲取的資料格式不正確或為空');
      }

      // 處理員工資料
      if (employeesResult?.error) {
        console.warn('載入員工資料失敗:', employeesResult.error.message);
        setCurrentEmployees([]);
      } else {
        setCurrentEmployees(employeesResult.data || []);
      }

      showSaveStatus('✓ 已從 API 讀取設定');
    } catch (error) {
      console.error('讀取資料時發生錯誤:', error);
      // 使用預設資料作為備援
      const { USER_PROVIDED_JSON } = await import('../constants');
      const { schedule, staff } = transformJsonToState(USER_PROVIDED_JSON, currentDepartmentId);
      setCurrentSchedule(schedule);
      setStaffData(staff);
      setCurrentEmployees([]); // 載入失敗時清空員工資料
      showSaveStatus(getErrorMessage(error), true);
    } finally {
      setIsLoading(false);
    }
  }, [currentDepartmentId, showSaveStatus]);

  // 儲存設定
  const handleSaveSettings = useCallback(async () => {
    if (!currentSchedule) {
      setActionModalConfig({
        title: '錯誤',
        message: '沒有可儲存的資料。',
        onConfirm: () => setActionModalConfig(null),
        onClose: () => setActionModalConfig(null),
      });
      return;
    }

    setIsLoading(true);
    setLoadingText('儲存設定...');

    try {
      const jsonData = transformStateToJson(currentSchedule, staffData, currentEmployees);
      const result = await shiftApi.saveRequirements(
        jsonData,
        processedData?.companyId || '',
        currentDepartmentId
      );

      if (result?.error) {
        throw new Error(result.error.message);
      }

      showSaveStatus('✓ 設定已儲存至伺服器');
    } catch (error) {
      console.error('儲存設定時發生錯誤:', error);
      setActionModalConfig({
        title: '儲存失敗',
        message: getErrorMessage(error),
        onConfirm: () => setActionModalConfig(null),
        onClose: () => setActionModalConfig(null),
      });
    } finally {
      setIsLoading(false);
    }
  }, [
    currentSchedule,
    staffData,
    currentEmployees,
    currentDepartmentId,
    showSaveStatus,
    processedData,
  ]);

  // 更新排班資料
  const handleScheduleUpdate = useCallback(
    (
      roleId: string,
      field: 'roleName' | 'remarks' | 'headcount' | 'roster',
      value: string | number | string[],
      timeslot: string | null = null
    ) => {
      setCurrentSchedule((prevSchedule) => {
        if (!prevSchedule) return null;

        const newSchedule = deepClone(prevSchedule);
        const role = newSchedule.roles.find((r) => r.id === roleId);
        if (!role) return prevSchedule;

        if (field === 'headcount' && typeof value === 'number' && timeslot) {
          if (!newSchedule.headcount[roleId]) newSchedule.headcount[roleId] = {};
          newSchedule.headcount[roleId][timeslot] = value;
        } else if (field === 'roleName' && typeof value === 'string') {
          role.name = value;
        } else if (field === 'remarks' && typeof value === 'string') {
          role.remarks = value;
        } else if (field === 'roster' && Array.isArray(value)) {
          role.roster = value;
        }

        return newSchedule;
      });
    },
    []
  );

  // 新增角色
  const handleAddRole = useCallback(() => {
    if (!currentSchedule) return;

    setCurrentSchedule((prevSchedule) => {
      if (!prevSchedule) return null;

      const newSchedule = deepClone(prevSchedule);
      const newRoleName = `新群組 ${(newSchedule.roles?.length || 0) + 1}`;
      const newRole = createDefaultRole(newRoleName);

      newSchedule.roles.push(newRole);
      return newSchedule;
    });
  }, [currentSchedule]);

  // Modal 管理
  const openRosterModal = useCallback((role: Role) => {
    setEditingRole(role);
    setIsRosterModalOpen(true);
  }, []);

  const closeRosterModal = useCallback(() => {
    setIsRosterModalOpen(false);
    setEditingRole(null);
  }, []);

  const closeActionModal = useCallback(() => {
    setActionModalConfig(null);
  }, []);

  // Action Modal 處理
  const handleActionConfirm = useCallback((type: ActionType, data: any) => {
    setCurrentSchedule((prevSchedule) => {
      if (!prevSchedule) return null;

      const newSchedule = deepClone(prevSchedule);

      switch (type) {
        case 'delete-role':
          newSchedule.roles = newSchedule.roles.filter((r) => r.id !== (data as Role).id);
          if (newSchedule.headcount) delete newSchedule.headcount[(data as Role).id];
          break;

        case 'add-timeslot': {
          const { start, end } = data as { start: string; end: string };

          if (!validateTimeRange(start, end)) {
            setActionModalConfig({
              title: '時間錯誤',
              message: '結束時間必須晚於開始時間。',
              onConfirm: () => showActionModal({ type: 'add-timeslot', data }),
              onClose: closeActionModal,
            });
            return prevSchedule;
          }

          if (isTimeOverlapping(newSchedule.timeslots || [], start, end)) {
            setActionModalConfig({
              title: '時間重疊',
              message: '您新增的時間段與現有時間重疊。',
              onConfirm: () => showActionModal({ type: 'add-timeslot', data }),
              onClose: closeActionModal,
            });
            return prevSchedule;
          }

          newSchedule.timeslots.push(`${start}-${end}`);
          break;
        }

        case 'delete-timeslot':
          newSchedule.timeslots = newSchedule.timeslots.filter((s) => s !== data);
          if (newSchedule.headcount) {
            Object.values(newSchedule.headcount).forEach(
              (roleSlots) => delete roleSlots[data as string]
            );
          }
          break;

        default:
          break;
      }

      return newSchedule;
    });

    closeActionModal();
  }, []);

  const showActionModal = useCallback(
    (config: { type: ActionType; data?: any }) => {
      let modalConfig: ActionModalConfig;
      const baseConfig = { onConfirm: handleActionConfirm, onClose: closeActionModal };

      if (config.type?.startsWith('delete')) {
        modalConfig = {
          ...baseConfig,
          type: config.type,
          data: config.data,
          title: '確認刪除',
          message: '確定要刪除此項目嗎？此操作無法復原。',
          mode: 'confirm',
        } as ActionModalConfig;
      } else if (config.type === 'add-timeslot') {
        const suggestedSlot = generateNextTimeslot(currentSchedule?.timeslots || []);

        modalConfig = {
          ...baseConfig,
          type: config.type,
          data: config.data || suggestedSlot,
          title: '新增時間段',
          mode: 'time-range',
        } as ActionModalConfig;
      } else {
        return;
      }

      setActionModalConfig(modalConfig);
    },
    [currentSchedule, handleActionConfirm]
  );

  // 計算屬性
  const scheduleWithRosterText = useMemo((): Schedule | null => {
    if (!currentSchedule) return null;
    return enhanceScheduleWithDisplayText(currentSchedule, staffData, currentEmployees);
  }, [currentSchedule, staffData, currentEmployees]);

  const staffAssignments = useMemo(() => {
    if (!currentSchedule) return {};
    return generateStaffAssignments(currentSchedule);
  }, [currentSchedule]);

  useEffect(() => {
    if (currentDepartmentId) {
      loadData();
    }
  }, [currentDepartmentId]);

  // 初始載入
  useEffect(() => {
    const departmentId = processedData?.departments?.[0]?.departmentId || '';
    if (departmentId) {
      setCurrentDepartmentId(departmentId);
    }
  }, []);

  return {
    // 狀態
    currentDepartmentId,
    currentSchedule,
    staffData,
    currentEmployees,
    isLoading,
    loadingText,
    saveStatus,
    isRosterModalOpen,
    editingRole,
    actionModalConfig,

    // 計算屬性
    scheduleWithRosterText,
    staffAssignments,

    // 動作
    setCurrentDepartmentId,
    loadData,
    handleSaveSettings,
    handleScheduleUpdate,
    handleAddRole,
    showSaveStatus,

    // Modal 相關
    openRosterModal,
    closeRosterModal,
    showActionModal,
    closeActionModal,
    handleActionConfirm,
  };
};
